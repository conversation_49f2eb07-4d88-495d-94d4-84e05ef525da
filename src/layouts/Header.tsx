import { Link, useNavigate } from '@tanstack/react-router';
import { useAuth } from '../contexts/AuthContext';

export const Header = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await signOut();
    navigate({ to: '/' });
  };

  return (
    <header className="bg-white shadow-md">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <Link to="/" className="flex items-center text-xl font-bold text-green-600">
              <img
                src="/Energieberatung.webp"
                alt="Energieausweis Logo"
                className="h-8 mr-2"
              />
              Energieausweis
            </Link>
          </div>
          <nav className="flex space-x-6">
            {user ? (
              <>
                <Link
                  to="/meine-zertifikate"
                  className="text-gray-600 hover:text-green-600 transition-colors"
                  activeProps={{ className: 'text-green-600 font-medium' }}
                >
                  Meine Zertifika<PERSON>
                </Link>
                <Link
                  to="/erfassen"
                  className="text-gray-600 hover:text-green-600 transition-colors"
                  activeProps={{ className: 'text-green-600 font-medium' }}
                >
                  Erfassen
                </Link>
                <Link
                  to="/konto"
                  className="text-gray-600 hover:text-green-600 transition-colors"
                  activeProps={{ className: 'text-green-600 font-medium' }}
                >
                  Konto
                </Link>
                <button
                  onClick={handleLogout}
                  className="text-gray-600 hover:text-green-600 transition-colors"
                >
                  Abmelden
                </button>
              </>
            ) : (
              <Link
                to="/login"
                className="text-gray-600 hover:text-green-600 transition-colors"
                activeProps={{ className: 'text-green-600 font-medium' }}
              >
                Login
              </Link>
            )}
          </nav>
        </div>
      </div>
    </header>
  );
};
