export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      energieausweise: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          objektdaten: Json | null
          gebaeudedetails1: Json | null
          gebaeudedetails2: Json | null
          kundendaten: Json | null
          fenster: Json | null
          heizung: Json | null
          trinkwarmwasser: Json | null
          lueftung: Json | null
          verbrauchsdaten: Json | null
          payment_status: string | null
          certificate_type: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          objektdaten?: Json | null
          gebaeudedetails1?: Json | null
          gebaeudedetails2?: Json | null
          kundendaten?: Json | null
          fenster?: Json | null
          heizung?: Json | null
          trinkwarmwasser?: Json | null
          lueftung?: Json | null
          verbrauchsdaten?: Json | null
          payment_status?: string | null
          certificate_type?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          objektdaten?: Json | null
          gebaeudedetails1?: Json | null
          gebaeudedetails2?: Json | null
          kundendaten?: Json | null
          fenster?: Json | null
          heizung?: Json | null
          trinkwarmwasser?: Json | null
          lueftung?: Json | null
          verbrauchsdaten?: Json | null
          payment_status?: string | null
          certificate_type?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}